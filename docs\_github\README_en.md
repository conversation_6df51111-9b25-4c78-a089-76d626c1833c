<div align="center">
  <a href="https://github.com/jasoneri/ComicGUISpider" target="_blank">
    <img src="../public/CGS-girl.png" alt="logo">
  </a>
  <h1 id="koishi"style="margin: 0.1em 0;">ComicGUISpider</h1>
  <img src="https://img.shields.io/badge/-3.12%2B-brightgreen.svg?logo=python" alt="tag">
  <img src="https://img.shields.io/badge/By-Qt5_&_Scrapy-blue.svg?colorA=abcdef" alt="tag">
  <img src="https://img.shields.io/badge/Platform-Win%20|%20macOS-blue?color=#4ec820" alt="tag">
  <a href="https://github.com/jasoneri/ComicGUISpider/releases" target="_blank">
    <img src="https://img.shields.io/github/downloads/jasoneri/ComicGUISpider/total?style=social&logo=github" alt="tag">
  </a>

  <p align="center">
  <a href="https://jasoneri.github.io/ComicGUISpider/locate/en/">🌐website</a> | 
  <a href="https://jasoneri.github.io/ComicGUISpider/locate/en/deploy/quick-start">🚀Quick-Start</a> | 
  <a href="https://github.com/jasoneri/ComicGUISpider/releases/latest">📦portable-pkg</a>
  </p>

</div>

▼ Demo ▼

|                             Preview / Multi-select / Paging                              |                         Clipboard Tasks                         |
|:-------------------------------------------------------------------------------:|:----------------------------------------------------------------------------:|
| ![turn-page-new](https://raw.githubusercontent.com/jasoneri/imgur/main/CGS/common-usage.gif) | ![load_clip](https://raw.githubusercontent.com/jasoneri/imgur/main/CGS/load_clip.gif) |

## 📑 Introduction

### Supported Websites

| Website                                 | locale |          Notes          |                                               status<br>(UTC+8)                                                |
|:----------------------------------------|:------:|:-----------------------:|:--------------------------------------------------------------------------------------------------------------:|
| [MangaCopy](https://www.2025copy.com/) |  :cn:  | Hidden content unlocked |  ![status_kaobei](https://img.shields.io/endpoint?url=https://cgs-status-badges.pages.dev/status_kaobei.json)  |
| [Māngabz](https://mangabz.com)          |  :cn:  |                         | ![status_mangabz](https://img.shields.io/endpoint?url=https://cgs-status-badges.pages.dev/status_mangabz.json) |
| [18comic](https://18comic.vip/)         |  :cn:  |           🔞            |      ![status_jm](https://img.shields.io/endpoint?url=https://cgs-status-badges.pages.dev/status_jm.json)      |
| [wnacg](https://www.wnacg.com/)         |  :cn:  |           🔞            |   ![status_wnacg](https://img.shields.io/endpoint?url=https://cgs-status-badges.pages.dev/status_wnacg.json)   |
| [ExHentai](https://exhentai.org/)       |   🌏   |           🔞            | ![status_ehentai](https://img.shields.io/endpoint?url=https://cgs-status-badges.pages.dev/status_ehentai.json) |
| [Hitomi](https://hitomi.la/)     | 🌏 |     🔞<br>need v2.2.0-beta     |  |
| [Kemono](https://kemono.cr)     | 🌏 |     🔞/[📒Usage](https://jasoneri.github.io/ComicGUISpider/feat/script)<br>need [v2.2.0-beta.2](https://github.com/jasoneri/ComicGUISpider/releases/tag/v2.2.0-beta.2)     |  |

<hr>

## 📜Contributing

now support simple `en-US` of Ui, but still need help for i18n of maintenance, such as Documentation  

Come here [🌏i18n Guide](../dev/i18n.md)

<hr>

## 📢 Changelog

Left-bottom of the config-dialog has `Check Update` button, please update according to the prompt

> [🕑Full History](docs/UPDATE_RECORD.md)

## 🚀 Usage

### GUI

`python CGS.py`

### CLI

`python crawl_only.py --help`  
Or using env of portable package:  
`.\runtime\python.exe .\scripts\crawl_only.py --help`

## 🔨 Configuration

[🔨Configuration](https://jasoneri.github.io/ComicGUISpider/locate/en/config)

## 🔇 Disclaimer

See [License](LICENSE). By using this project you agree to:

- Non-commercial use only
- Developer's final interpretation

---
![CGS_en](https://count.getloli.com/get/@CGS_en?theme=rule34)
